# Database Query Templates

## Common Analysis Queries

### 1. Table Overview
```sql

        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            TABLE_TYPE as 'table_type',
            <PERSON>NGIN<PERSON> as 'engine',
            TABLE_ROWS as 'row_count',
            ROUND((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) as 'size_mb',
            CREATE_TIME as 'created_at',
            UPDATE_TIME as 'updated_at',
            TABLE_COMMENT as 'comment'
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        ORDER BY TABLE_SCHEMA, TABLE_NAME
    
```

### 2. Column Details
```sql

        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            COLUMN_NAME as 'column_name',
            ORDI<PERSON>L_POSITION as 'position',
            COLUMN_DEFAULT as 'default_value',
            <PERSON>_<PERSON>U<PERSON><PERSON><PERSON> as 'nullable',
            <PERSON><PERSON><PERSON>_TYPE as 'data_type',
            CHARACTER_MAXIMUM_LENGTH as 'max_length',
            NUMERIC_PRECISION as 'precision',
            NUMERIC_SCALE as 'scale',
            COLUMN_TYPE as 'full_type',
            COLUMN_KEY as 'key_type',
            EXTRA as 'extra',
            COLUMN_COMMENT as 'comment'
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        ORDER BY TABLE_SCHEMA, TABLE_NAME, ORDINAL_POSITION
    
```

### 3. Foreign Key Relationships
```sql

        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            COLUMN_NAME as 'column_name',
            REFERENCED_TABLE_SCHEMA as 'referenced_database',
            REFERENCED_TABLE_NAME as 'referenced_table',
            REFERENCED_COLUMN_NAME as 'referenced_column',
            CONSTRAINT_NAME as 'constraint_name'
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        AND REFERENCED_TABLE_NAME IS NOT NULL
        ORDER BY TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME
    
```

### 4. Index Information
```sql

        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            INDEX_NAME as 'index_name',
            COLUMN_NAME as 'column_name',
            NON_UNIQUE as 'non_unique',
            INDEX_TYPE as 'index_type',
            SEQ_IN_INDEX as 'sequence'
        FROM information_schema.STATISTICS 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        ORDER BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
    
```

## Logistics-Specific Queries

### Orders Analysis
```sql
-- Recent orders
SELECT * FROM channelconnector.orders 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY created_at DESC;

-- Orders by channel
SELECT channel, COUNT(*) as order_count, SUM(total_amount) as total_value
FROM channelconnector.orders 
GROUP BY channel
ORDER BY order_count DESC;
```

### Products Analysis
```sql
-- Product inventory status
SELECT sku, title, inventory_quantity, channel
FROM channelconnector.products 
WHERE inventory_quantity < 10
ORDER BY inventory_quantity ASC;
```

### User Activity
```sql
-- Active users
SELECT name, email, last_login, role
FROM channelconnector.users 
WHERE last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY last_login DESC;
```
