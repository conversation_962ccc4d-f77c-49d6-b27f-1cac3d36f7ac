#!/usr/bin/env node

/**
 * Test database connection for MCP MySQL server
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

async function testConnection() {
    console.log('🔍 Testing database connection...');

    const config = {
        host: process.env.DB_HOST,
        port: parseInt(process.env.DB_PORT) || 3306,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        connectTimeout: 10000,
        acquireTimeout: 10000,
        timeout: 10000
    };
    
    console.log(`Connecting to: ${config.host}:${config.port} as ${config.user}`);
    
    try {
        // Test basic connection
        const connection = await mysql.createConnection(config);
        console.log('✅ Database connection successful!');
        
        // Test database access
        const [databases] = await connection.execute('SHOW DATABASES');
        console.log('\n📊 Available databases:');
        databases.forEach(db => {
            console.log(`  - ${db.Database}`);
        });
        
        // Test channelconnector database
        if (databases.some(db => db.Database === 'channelconnector')) {
            console.log('\n🔍 Analyzing channelconnector database...');
            
            const [tables] = await connection.execute(`
                SELECT TABLE_NAME, TABLE_ROWS, 
                       ROUND((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) as 'SIZE_MB'
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = 'channelconnector'
                ORDER BY TABLE_ROWS DESC
                LIMIT 10
            `);
            
            console.log('\n📋 Top 10 tables by row count:');
            tables.forEach(table => {
                console.log(`  - ${table.TABLE_NAME}: ${table.TABLE_ROWS} rows (${table.SIZE_MB} MB)`);
            });
            
            // Check for authentication tables
            const [authTables] = await connection.execute(`
                SELECT TABLE_NAME, TABLE_ROWS
                FROM information_schema.TABLES 
                WHERE TABLE_SCHEMA = 'channelconnector'
                AND (TABLE_NAME LIKE '%user%' OR TABLE_NAME LIKE '%auth%' OR TABLE_NAME LIKE '%role%')
                ORDER BY TABLE_NAME
            `);
            
            if (authTables.length > 0) {
                console.log('\n🔐 Authentication-related tables:');
                authTables.forEach(table => {
                    console.log(`  - ${table.TABLE_NAME}: ${table.TABLE_ROWS} rows`);
                });
            }
        }
        
        await connection.end();
        console.log('\n🎉 Database analysis complete!');
        
        return true;
        
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('💡 Possible solutions:');
            console.log('  - Check if MySQL server is running');
            console.log('  - Verify the host and port are correct');
            console.log('  - Check firewall settings');
        } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('💡 Possible solutions:');
            console.log('  - Verify username and password');
            console.log('  - Check user permissions');
            console.log('  - Ensure user can connect from this host');
        }
        
        return false;
    }
}

if (require.main === module) {
    testConnection().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { testConnection };
