/**
 * Authentication middleware with JWT blacklisting
 */
const jwt = require('jsonwebtoken');
const config = require('../config');
const logger = require('../utils/logger');
const NodeCache = require('node-cache');

// JWT blacklist cache (TTL should match JWT expiration)
const jwtBlacklist = new NodeCache({ stdTTL: 86400, checkperiod: 120 }); // 24 hours

/**
 * Add token to blacklist
 * @param {string} token - The JWT token to blacklist
 */
exports.blacklistToken = (token) => {
  try {
    const decoded = jwt.decode(token);
    if (decoded && decoded.exp) {
      const ttl = decoded.exp - Math.floor(Date.now() / 1000);
      if (ttl > 0) {
        jwtBlacklist.set(token, true, ttl);
        logger.info('Token blacklisted', { tokenId: decoded.jti || 'unknown' });
      }
    }
  } catch (error) {
    logger.error('Error blacklisting token', { error: error.message });
  }
};

/**
 * Check if token is blacklisted
 * @param {string} token - The JWT token to check
 * @returns {boolean} - True if token is blacklisted
 */
exports.isTokenBlacklisted = (token) => {
  return jwtBlacklist.has(token);
};

/**
 * Middleware to authenticate JWT token
 * @param {object} req - The request object
 * @param {object} res - The response object
 * @param {function} next - The next middleware function
 */
exports.authenticateToken = (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Access denied. No token provided.'
      });
    }
    
    // Check if token is blacklisted
    if (exports.isTokenBlacklisted(token)) {
      return res.status(401).json({
        success: false,
        message: 'Token has been revoked.'
      });
    }
    
    // Verify token
    jwt.verify(token, config.jwt.secret, (err, user) => {
      if (err) {
        return res.status(403).json({
          success: false,
          message: 'Invalid token.'
        });
      }
      
      // Set user and token in request
      req.user = user;
      req.token = token;
      next();
    });
  } catch (error) {
    logger.error('Error authenticating token', { error: error.message });
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Middleware to logout and blacklist token
 * @param {object} req - The request object
 * @param {object} res - The response object
 */
exports.logout = (req, res) => {
  try {
    const token = req.token;
    
    if (token) {
      exports.blacklistToken(token);
    }
    
    return res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    logger.error('Error during logout', { error: error.message });
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};

/**
 * Middleware to check permission
 * @returns {function} - The middleware function
 */
exports.checkPermission = () => {
  return (req, res, next) => {
    try {
      // Check if user has required role
      const user = req.user;
      
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Access denied. User not authenticated.'
        });
      }
      
      // Set user roles in request
      req.userRoles = {
        isRoleSuperAdmin: user.role === 'superadmin',
        isRoleAdmin: user.role === 'admin' || user.role === 'superadmin',
        isRoleUser: user.role === 'user' || user.role === 'admin' || user.role === 'superadmin'
      };
      
      next();
    } catch (error) {
      logger.error('Error checking permission', { error: error.message });
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  };
};
