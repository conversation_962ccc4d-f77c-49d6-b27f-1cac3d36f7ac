/**
 * Main application file with security enhancements
 */
require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const swaggerUI = require('swagger-ui-express');
const YAML = require('yamljs');
const config = require('./config');
const logger = require('./utils/logger');
const routes = require('./api/routes');

// Security middleware
const security = require('./middleware/security');
const { generalLimiter } = require('./middleware/rateLimiter');
const { sanitizeInput } = require('./middleware/validation');

// Create Express app
const app = express();

// Trust proxy (important for rate limiting and logging)
app.set('trust proxy', 1);

// Security headers
app.use(security.securityHeaders);
app.use(security.securityResponseHeaders);

// Request logging
app.use(security.requestLogger);

// Rate limiting
app.use(generalLimiter);

// Input sanitization and security
app.use(security.xssClean);
app.use(security.mongoSanitize);
app.use(security.preventHPP);
app.use(security.suspiciousActivityDetector);
app.use(sanitizeInput);

// CORS configuration
const corsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = [
      'http://logistics-dev.centralindia.cloudapp.azure.com',
      'http://logistics-uat.centralindia.cloudapp.azure.com',
      'http://localhost:5173',
      'http://localhost:5000',
      'http://127.0.0.1'
    ];

    if (allowedOrigins.includes(origin) || !origin) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  preflightContinue: false
};

// Apply middleware
app.use(cors(corsOptions));
app.use(express.json({ limit: '10mb' })); // Limit request size
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static(path.join(__dirname, '../uploads')));

// API routes
app.use('/api', routes);

// Legacy routes - these will be migrated incrementally
app.use('/', require('../server/routes/router.js'));
app.use('/client', require('../routes/_1_client/client_routes'));
app.use('/channel', require('../routes/_2_channel/channel_routes'));
app.use('/prestashop', require('../routes/_3_prestashop/prestashop_routes'));
app.use('/ebay', require('../routes/_4_ebay/ebay_routes'));

// Swagger documentation
const swaggerPath = path.join(__dirname, '../swagger.yaml');
const swaggerDocument = YAML.load(swaggerPath);
app.use('/swagger', swaggerUI.serve, swaggerUI.setup(swaggerDocument));

// 404 handler
app.use(security.notFoundHandler);

// Error handling middleware
app.use(security.errorHandler);

module.exports = app;
