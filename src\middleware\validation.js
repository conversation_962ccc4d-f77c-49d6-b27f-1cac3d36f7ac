/**
 * Schema validation middleware
 */
const Joi = require('joi');
const logger = require('../utils/logger');

/**
 * Validate request data against a Joi schema
 * @param {object} schema - Joi schema object with body, query, params properties
 * @returns {function} - Validation middleware
 */
exports.validate = (schema) => {
  return (req, res, next) => {
    try {
      const validationErrors = [];
      
      // Validate request body
      if (schema.body) {
        const { error } = schema.body.validate(req.body);
        if (error) {
          validationErrors.push({
            field: 'body',
            message: error.details[0].message,
            path: error.details[0].path
          });
        }
      }
      
      // Validate query parameters
      if (schema.query) {
        const { error } = schema.query.validate(req.query);
        if (error) {
          validationErrors.push({
            field: 'query',
            message: error.details[0].message,
            path: error.details[0].path
          });
        }
      }
      
      // Validate route parameters
      if (schema.params) {
        const { error } = schema.params.validate(req.params);
        if (error) {
          validationErrors.push({
            field: 'params',
            message: error.details[0].message,
            path: error.details[0].path
          });
        }
      }
      
      // If there are validation errors, return them
      if (validationErrors.length > 0) {
        logger.warn('Validation failed', {
          endpoint: req.originalUrl,
          method: req.method,
          errors: validationErrors
        });
        
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationErrors
        });
      }
      
      next();
    } catch (error) {
      logger.error('Error in validation middleware', { error: error.message });
      
      return res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  };
};

/**
 * Common validation schemas
 */
exports.schemas = {
  // FBY User ID validation
  fbyUserId: {
    query: Joi.object({
      fby_user_id: Joi.string().required().min(1).max(128)
        .messages({
          'string.empty': 'FBY User ID cannot be empty',
          'any.required': 'FBY User ID is required',
          'string.max': 'FBY User ID cannot exceed 128 characters'
        })
    })
  },
  
  // Order validation
  createOrder: {
    body: Joi.object({
      fby_user_id: Joi.string().required().min(1).max(128),
      channel: Joi.string().required().valid('Shopify', 'Amazon', 'eBay', 'Mirakl', 'Magento', 'WooCommerce', 'Prestashop', 'Storeden'),
      order_no: Joi.string().required().min(1).max(256),
      seller_order_id: Joi.string().optional().max(100),
      purchase_date: Joi.date().optional(),
      payment_date: Joi.date().optional(),
      customer_name: Joi.string().optional().max(256),
      customer_email: Joi.string().email().optional().max(256),
      customer_phone: Joi.string().optional().max(256),
      shipping_address: Joi.string().optional().max(256),
      shipping_city: Joi.string().optional().max(256),
      shipping_state: Joi.string().optional().max(256),
      shipping_country: Joi.string().optional().max(256),
      shipping_zipcode: Joi.string().optional().max(256),
      currency: Joi.string().optional().max(20),
      total_amount: Joi.number().precision(2).optional(),
      total_shipping_amount: Joi.number().precision(2).optional(),
      total_tax_amount: Joi.number().precision(2).optional(),
      payment_status: Joi.string().optional().max(20),
      order_status: Joi.string().optional().max(20)
    })
  },
  
  updateOrder: {
    params: Joi.object({
      orderId: Joi.string().required().min(1).max(256)
    }),
    body: Joi.object({
      customer_name: Joi.string().optional().max(256),
      customer_email: Joi.string().email().optional().max(256),
      customer_phone: Joi.string().optional().max(256),
      shipping_address: Joi.string().optional().max(256),
      shipping_city: Joi.string().optional().max(256),
      shipping_state: Joi.string().optional().max(256),
      shipping_country: Joi.string().optional().max(256),
      shipping_zipcode: Joi.string().optional().max(256),
      payment_status: Joi.string().optional().max(20),
      order_status: Joi.string().optional().max(20)
    })
  },
  
  updateOrderStatus: {
    body: Joi.object({
      orderId: Joi.string().required().min(1).max(256),
      status: Joi.string().required().valid('pending', 'processing', 'shipped', 'delivered', 'cancelled')
    })
  },
  
  // Product validation
  createProduct: {
    body: Joi.object({
      fby_user_id: Joi.string().required().min(1).max(128),
      channel: Joi.string().required().valid('Shopify', 'Amazon', 'eBay', 'Mirakl', 'Magento', 'WooCommerce', 'Prestashop', 'Storeden'),
      sku: Joi.string().required().min(1).max(128),
      barcode: Joi.string().optional().max(128),
      title: Joi.string().required().min(1).max(128),
      price: Joi.number().precision(2).min(0).optional(),
      inventory_quantity: Joi.number().integer().min(0).optional(),
      image: Joi.string().uri().optional()
    })
  },
  
  // Pagination validation
  pagination: {
    query: Joi.object({
      page: Joi.number().integer().min(1).default(1),
      pageSize: Joi.number().integer().min(1).max(100).default(25),
      sortBy: Joi.string().optional(),
      sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    })
  },
  
  // Authentication validation
  login: {
    body: Joi.object({
      username: Joi.string().required().min(3).max(50),
      password: Joi.string().required().min(6).max(128)
    })
  },
  
  register: {
    body: Joi.object({
      name: Joi.string().required().min(2).max(50),
      email: Joi.string().email().required().max(50),
      username: Joi.string().required().min(3).max(50),
      password: Joi.string().required().min(6).max(128),
      fby_user_id: Joi.string().required().min(1).max(128),
      auth_username: Joi.string().optional().max(128),
      auth_password: Joi.string().optional().max(128)
    })
  }
};

/**
 * Sanitize input data
 * @param {object} data - Data to sanitize
 * @returns {object} - Sanitized data
 */
exports.sanitize = (data) => {
  if (typeof data !== 'object' || data === null) {
    return data;
  }
  
  const sanitized = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (typeof value === 'string') {
      // Remove potentially dangerous characters
      sanitized[key] = value
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
        .replace(/javascript:/gi, '') // Remove javascript: protocol
        .replace(/on\w+\s*=/gi, '') // Remove event handlers
        .trim();
    } else if (typeof value === 'object' && value !== null) {
      sanitized[key] = exports.sanitize(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
};

/**
 * Middleware to sanitize request data
 */
exports.sanitizeInput = (req, res, next) => {
  try {
    if (req.body) {
      req.body = exports.sanitize(req.body);
    }
    
    if (req.query) {
      req.query = exports.sanitize(req.query);
    }
    
    if (req.params) {
      req.params = exports.sanitize(req.params);
    }
    
    next();
  } catch (error) {
    logger.error('Error sanitizing input', { error: error.message });
    
    return res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: error.message
    });
  }
};
