const constants = require("../constants/constants.js");
const common = require("../constants/common.js");
const logger = require("../../misc/logger");
const request = require("request-promise");
const helpers = require("../../misc/helpers");
const miscConstants = require("../../misc/constants");
const shopifyController = require("./shopifyController.js");
const storedenController = require("./storedenController.js");
const fbyService = require("../../services/fby_service");
const clientService = require('../../services/hcl/clientService.js');
const dateTime = require("node-datetime");
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

const prestashopController = require("../../services/prestashopService/prestashop_service");

const { createProductWoocommerce } = require("./woocommerceController.js");
const { createProductMagento } = require("./magentoController.js");

exports.login = async (req, res) => {
    const { email=null, mobile = null, password } = req.body;

    try {
        common.getAuthUserDetails(email, mobile, async function (users) {
            if (users.error) {
                return false;
            } else {
                if (users.success.data.length === 0) {
                    return res.status(401).json({ error: 'Invalid credentials' });
                }
                const user = users.success.data[0];
                // Compare the provided password with the hashed password in the database
                const passwordMatch = await bcrypt.compare(password, user.password);

                if (!passwordMatch) {
                    return res.status(401).json({ error: 'Invalid credentials' });
                }

                const userData = users.success.data;
                // Organize roles and permissions
                const roles = [];
                const permissions = [];
                userData.forEach((row) => {
                    if (row.role_name && !roles.includes(row.role_name)) {
                        roles.push(row.role_name);
                    }
                    if (row.permission_name && !permissions.includes(row.permission_name)) {
                        permissions.push(row.permission_name);
                    }
                });
        
                const client = await clientService.getClientById(user.hcl_client_id);  
                // Generate a JWT token
                const token = jwt.sign(
                    { 
                        id: user.id, 
                        name: user.name, 
                        email: user.email, 
                        groupCode: user.groupCode, 
                        clientId: user.hcl_client_id, 
                        organizationId: user.organization_id,
                        roleId: user.role_id, 
                        roles, 
                        permissions,
                        client: client.length > 0 ? client[0] : {}
                    }, 
                    process.env.JWT_KEY, 
                    { expiresIn: process.env.JWT_EXPIRES_IN }
                );
                const groupCode = user.groupCode;
                res.json({ 
                    token, 
                    user: { 
                        id: user.id, 
                        name: user.name, 
                        email: user.email, 
                        groupCode: user.groupCode, 
                        clientId: user.hcl_client_id, 
                        organizationId: user.organization_id,
                        roleId: user.role_id,
                        roles, 
                        permissions,
                        client: client.length > 0 ? client[0] : {}
                    }, 
                    groupCode 
                });
            }
        })

    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ error: 'Internal Server Error' });
    }
}

/**
 * signup controller 
 * @param {*} req 
 * @param {*} res 
 */
exports.signup = async (req, res) => {
    try {
        const {
            name,
            email,
            mobile = null,
            password = null,
            groupCode,
            clientId = null,
            organizationId = null,
            roleId = 6
        } = req.body;

        let createdBy = 0;
        let finalOrganizationId = organizationId;
        let finalClientId = clientId;

        if (req?.user) {
            const { id, organizationId: userOrgId } = req.user;
            createdBy = id;
            finalOrganizationId = finalOrganizationId || userOrgId;
            // Uncomment to set clientId from user (optional)
            // finalClientId = finalClientId || req.user.clientId;
        }

        // Call signup service
        const result = await common.signup({
            name,
            email,
            mobile,
            password,
            groupCode,
            clientId: finalClientId,
            organizationId: finalOrganizationId,
            roleId,
            createdBy
        });

        if (result.success) {
            res.status(201).json(result);
        } else {
            res.status(400).json(result);  // 400 for bad request (e.g., user already exists)
        }
    } catch (error) {
        console.error('Signup error:', error);

        // Return proper error response
        res.status(500).json({
            error: error.message || 'Internal Server Error'
        });
    }
};

exports.getAllAuthUsers = async (req, res) => {
     // Extract pagination parameters
    const { groupCode = null, page = 1, pageSize = 10 } = req.query
    const userData = req.user;
    let organizationId = userData.organizationId;
    const isSuperAdmin = userData.roles.includes('Super Admin');
    if(isSuperAdmin) {
        organizationId = null;
    }
     // Parse pagination values to integers
     const pageNum = parseInt(page, 10) || 1
     const pageSizeNum = parseInt(pageSize, 10) || 10
    try {
        const { usersWithClients, totalRecords } = await common.getAllAuthUsersWithClientDetails(organizationId, groupCode, parseInt(pageNum), parseInt(pageSizeNum))
        helpers.sendPaginationResponse(res, 200, 'Users fetched successfully', usersWithClients, totalRecords, pageNum, pageSizeNum)
    } catch (error) {
        helpers.sendError(res, 500, 'getAllAuthUsers', error.message, req.query)
    }
};

exports.editAuthUser = async (req, res) => {
    const { id } = req.params
    const userData = req.body
    try {
        const result = await common.updateAuthUser(id, userData)
        helpers.sendSuccess(res, 200, 'User updated successfully!', result, userData)
    } catch (error) {
        helpers.sendError(res, 400, 'editAuthUser', error.message, userData)
    }
};

exports.getAuthtUserById = async (req, res) => {
    const { id } = req.params
    try {
        const user = await common.getAuthUserById(id)
        helpers.sendSuccess(res, 200, 'User fetched successfully!', user, req.params)
    } catch (error) {
        helpers.sendError(res, 400, 'getAuthtUserById', error.message, req.params)
    }
};

exports.deleteAuthUser = async (req, res) => {
    const { id } = req.params 
    try {
        const result = await common.deleteAuthUser(id);
        helpers.sendSuccess(res, 200, 'User deleted successfully!', result, req.params)
    } catch (error) {
        helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.params)
    }
  };

exports.changePassword = async (req, res) => {
    const { oldPassword, newPassword } = req.body;
    const userData = req.user;
    try {
   
        const result = await common.changePassword(userData, oldPassword, newPassword);
        if(result.success === true) {
            return helpers.sendSuccess(res, miscConstants.HTTPSTATUSCODES.OK, 
                miscConstants.SUCESSSMESSAGES.UPDATE, {message: result.message} , req.body);
        } else {
            return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.BAD_REQUEST, 
                miscConstants.ERRORCODES.NOT_FOUND, result.message, req.body);
        }
   
    } catch (error) {
   
        return helpers.sendError(res, miscConstants.HTTPSTATUSCODES.INTERNAL_SERVER_ERROR, 
        miscConstants.ERRORCODES.INTERNAL_SERVER_ERROR, error.message, req.body);
   
    }
};