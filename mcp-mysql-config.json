{"mcpServers": {"logistics_mysql": {"command": "npx", "args": ["-y", "@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "localhost", "MYSQL_PORT": "3306", "MYSQL_USER": "root", "MYSQL_PASS": "your_password", "MYSQL_DB": "", "MYSQL_SSL": "false", "MYSQL_SSL_REJECT_UNAUTHORIZED": "true", "MYSQL_POOL_SIZE": "10", "MYSQL_QUERY_TIMEOUT": "30000", "MYSQL_CACHE_TTL": "60000", "MYSQL_RATE_LIMIT": "100", "MYSQL_MAX_QUERY_COMPLEXITY": "1000", "ALLOW_INSERT_OPERATION": "false", "ALLOW_UPDATE_OPERATION": "false", "ALLOW_DELETE_OPERATION": "false", "ALLOW_DDL_OPERATION": "false", "SCHEMA_INSERT_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "SCHEMA_UPDATE_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "SCHEMA_DELETE_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "SCHEMA_DDL_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "MULTI_DB_WRITE_MODE": "false", "ENABLE_LOGGING": "true", "MYSQL_LOG_LEVEL": "info", "MYSQL_METRICS_ENABLED": "true", "PATH": "/usr/local/bin:/usr/bin:/bin"}}}}