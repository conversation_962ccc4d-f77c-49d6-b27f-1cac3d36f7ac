{"mcpServers": {"logistics_mysql": {"command": "npx", "args": ["-y", "@benborla29/mcp-server-mysql"], "env": {"MYSQL_HOST": "*************", "MYSQL_PORT": "3306", "MYSQL_USER": "san", "MYSQL_PASS": "DB@2024+nimda!@", "MYSQL_DB": "", "MYSQL_SSL": "false", "MYSQL_SSL_REJECT_UNAUTHORIZED": "true", "MYSQL_POOL_SIZE": "10", "MYSQL_QUERY_TIMEOUT": "30000", "MYSQL_CACHE_TTL": "60000", "MYSQL_RATE_LIMIT": "100", "MYSQL_MAX_QUERY_COMPLEXITY": "1000", "ALLOW_INSERT_OPERATION": "false", "ALLOW_UPDATE_OPERATION": "false", "ALLOW_DELETE_OPERATION": "false", "ALLOW_DDL_OPERATION": "false", "SCHEMA_INSERT_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "SCHEMA_UPDATE_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "SCHEMA_DELETE_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "SCHEMA_DDL_PERMISSIONS": "channelconnector:false,hcl:false,cclogs:false", "MULTI_DB_WRITE_MODE": "false", "ENABLE_LOGGING": "true", "MYSQL_LOG_LEVEL": "info", "MYSQL_METRICS_ENABLED": "true", "PATH": "E:\\_work\\logistics\\logistics_backend\\node_modules\\.bin;E:\\_work\\logistics\\node_modules\\.bin;E:\\_work\\node_modules\\.bin;E:\\node_modules\\.bin;C:\\ProgramData\\nvm\\v18.20.3\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;E:\\Python313\\Scripts\\;E:\\Python313\\;C:\\Program Files\\Microsoft MPI\\Bin\\;C:\\Program Files\\Microsoft SDKs\\Azure\\CLI2\\wbin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\ProgramData\\chocolatey\\bin;C:\\Program Files (x86)\\Microsoft SQL Server\\160\\DTS\\Binn\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;E:\\Search;C:\\ProgramData\\nvm;C:\\Program Files\\nodejs;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files\\PuTTY\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Microsoft SQL Server\\130\\Tools\\Binn\\;C:\\Program Files\\GitHub CLI\\;C:\\Program Files\\dotnet\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\MySQL\\MySQL Shell 8.0\\bin\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Program Files\\IrfanView\\iv.exe;C:\\Users\\<USER>\\AppData\\Roaming\\npm;E:\\Search;C:\\ProgramData\\nvm;C:\\Program Files\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Program Files (x86)\\MongoDB Atlas CLI\\;C:\\Users\\<USER>\\.dotnet\\tools;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand"}}}}