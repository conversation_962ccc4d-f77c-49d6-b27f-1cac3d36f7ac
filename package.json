{"name": "channelconnector", "version": "1.0.0", "description": "Channels Connector", "main": "app.js", "type": "commonjs", "scripts": {"test": "jest", "test:watch": "jest --watch", "start": "node app.js", "dev": "nodemon app.js", "build": "npm-run-all build:clean build:compile", "build:clean": "rimraf dist && mkdir dist", "build:compile": "npx babel app.js -d dist", "mcp:setup": "node scripts/setup-mcp-mysql.js", "mcp:docs": "node scripts/generate-db-docs.js", "mcp:test": "npx -y @benborla29/mcp-server-mysql", "mcp:test-simple": "node scripts/simple-mcp-test.js", "mcp:generate-config": "node scripts/generate-claude-config.js", "db:analyze": "mysql -u root -p < scripts/database-analysis.sql"}, "dependencies": {"@azure/storage-blob": "^12.11.0", "@joi/date": "^2.1.0", "@woocommerce/woocommerce-rest-api": "^1.0.1", "amazon-sp-api": "^0.7.9", "async-retry": "^1.3.3", "axios": "^0.24.0", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "body-parser": "^1.19.1", "chai": "^3.5.0", "circular-json": "^0.5.9", "cookie-parser": "^1.4.5", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^10.0.0", "ejs": "^3.1.6", "enhanced-resolve": "^5.10.0", "esm": "^3.2.25", "express": "^4.21.1", "fast-csv": "^5.0.2", "fast-parquet": "^1.0.3", "fast-xml-parser": "^4.3.6", "formidable": "^1.2.2", "fs": "^0.0.1-security", "generate-password": "^1.7.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "mirakl": "^3.0.3", "mirakl-connector": "^1.0.22", "moment": "^2.29.1", "mongoose": "^8.8.0", "morgan": "^1.10.0", "mssql": "^11.0.1", "multer": "^1.4.5-lts.1", "mysql": "^2.18.1", "mysql2": "^3.11.0", "node-cache": "^5.1.2", "node-cron": "^3.0.0", "node-datetime": "^2.1.2", "node-gyp": "^9.0.0", "node-mysql-deadlock-retries": "^2.0.5", "nodemailer": "^6.7.2", "nodemon": "^2.0.7", "os": "^0.1.2", "razorpay": "^2.0.6", "request": "^2.88.2", "request-promise": "^0.0.1", "rxjs": "^7.0.1", "save": "^2.9.0", "slashes": "^2.0.2", "swagger-jsdoc": "^6.1.0", "swagger-ui-express": "^4.1.6", "title-case": "^3.0.3", "urlencode": "^1.1.0", "uuid": "^8.3.2", "winston": "^3.4.0", "winston-sql-transport": "^3.1.0", "xlsx": "^0.18.5", "xml-js": "^1.6.11", "xml2js": "^0.6.2", "yamljs": "^0.3.0"}, "author": "SK", "license": "DS", "devDependencies": {"@babel/cli": "^7.26.4", "@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@types/mysql": "^2.15.20", "@types/request-promise": "^4.1.48", "babel-cli": "^6.26.0", "babel-core": "^6.26.3", "babel-polyfill": "^6.26.0", "babel-preset-es2015": "^6.24.1", "babel-preset-stage-2": "^6.11.0", "babel-register": "^6.26.0", "jest": "^29.7.0", "mocha": "^11.1.0", "nodemon": "^2.0.15", "npm-run-all": "^4.1.5", "rimraf": "^3.0.2", "supertest": "^7.0.0", "swagger-autogen": "^2.13.0"}, "keywords": []}