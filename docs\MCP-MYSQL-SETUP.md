# MySQL MCP Server Setup for Logistics Backend

This document explains how to configure and use the MySQL MCP (Model Context Protocol) server to enhance AI-assisted database management and analysis for your logistics backend.

## What is MCP?

The Model Context Protocol (MCP) allows AI assistants like <PERSON> to directly interact with external systems, including databases. The MySQL MCP server provides:

- **Database Schema Inspection**: Automatically discover and understand your database structure
- **Safe Query Execution**: Execute read-only queries with built-in security controls
- **Multi-Database Support**: Access multiple databases in your logistics system
- **Real-time Analysis**: Get instant insights about your data without manual SQL writing

## Quick Setup

### 1. Install the MCP Server

```bash
# Run the automated setup script
npm run mcp:setup

# Or install manually
npm install -g @benborla29/mcp-server-mysql
```

### 2. Configure Database Connection

Update the `.env.mcp` file with your MySQL credentials:

```env
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_mysql_user
MYSQL_PASS=your_mysql_password
MYSQL_DB=
# Leave MYSQL_DB empty for multi-database mode
```

### 3. Configure <PERSON>

Add the configuration to your Claude Desktop config file:

**Location**: 
- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "logistics_mysql": {
      "command": "npx",
      "args": ["-y", "@benborla29/mcp-server-mysql"],
      "env": {
        "MYSQL_HOST": "localhost",
        "MYSQL_PORT": "3306",
        "MYSQL_USER": "your_user",
        "MYSQL_PASS": "your_password",
        "MYSQL_DB": "",
        "ALLOW_INSERT_OPERATION": "false",
        "ALLOW_UPDATE_OPERATION": "false",
        "ALLOW_DELETE_OPERATION": "false",
        "ENABLE_LOGGING": "true"
      }
    }
  }
}
```

### 4. Restart Claude Desktop

After configuration, restart Claude Desktop to load the MCP server.

## Usage Examples

Once configured, you can ask Claude to:

### Database Structure Analysis
- "Show me all tables in the channelconnector database"
- "Describe the structure of the orders table"
- "What are the foreign key relationships in my database?"
- "List all indexes on the products table"

### Data Analysis
- "How many orders were created in the last 7 days?"
- "Show me the top 5 channels by order volume"
- "Which products have low inventory?"
- "What's the average order value by channel?"

### Authentication Analysis
- "Show me all user-related tables"
- "How many active users do we have?"
- "What roles and permissions exist in the system?"

### Performance Analysis
- "Which tables are the largest?"
- "Show me tables without primary keys"
- "What indexes are missing on frequently queried columns?"

## Security Features

### Read-Only by Default
The MCP server is configured in read-only mode by default for safety:
- `ALLOW_INSERT_OPERATION=false`
- `ALLOW_UPDATE_OPERATION=false`
- `ALLOW_DELETE_OPERATION=false`
- `ALLOW_DDL_OPERATION=false`

### Schema-Specific Permissions
You can configure different permission levels for different databases:

```env
SCHEMA_INSERT_PERMISSIONS=development:true,production:false
SCHEMA_UPDATE_PERMISSIONS=development:true,production:false
SCHEMA_DELETE_PERMISSIONS=development:false,production:false
```

### Connection Security
- Connection pooling with configurable limits
- Query timeout protection
- Rate limiting to prevent abuse
- SSL/TLS support for encrypted connections

## Advanced Configuration

### Performance Tuning
```env
MYSQL_POOL_SIZE=10
MYSQL_QUERY_TIMEOUT=30000
MYSQL_CACHE_TTL=60000
```

### Monitoring
```env
ENABLE_LOGGING=true
MYSQL_LOG_LEVEL=info
MYSQL_METRICS_ENABLED=true
```

### Multi-Database Mode
Leave `MYSQL_DB` empty to access all databases:
```env
MYSQL_DB=
```

In multi-DB mode, use fully qualified table names:
```sql
SELECT * FROM channelconnector.orders;
SELECT * FROM hcl.users;
```

## Troubleshooting

### Connection Issues
1. Verify MySQL is running: `mysql -u root -p`
2. Check credentials in `.env.mcp`
3. Ensure user has appropriate permissions
4. Test connection: `npm run mcp:test`

### Claude Desktop Issues
1. Check logs: `~/Library/Logs/Claude/mcp-server-logistics_mysql.log`
2. Verify configuration file syntax
3. Restart Claude Desktop after changes
4. Ensure Node.js paths are correct

### Permission Errors
1. Grant necessary MySQL permissions:
```sql
GRANT SELECT ON *.* TO 'your_user'@'localhost';
FLUSH PRIVILEGES;
```

2. For write operations (if needed):
```sql
GRANT INSERT, UPDATE, DELETE ON channelconnector.* TO 'your_user'@'localhost';
```

## Available Scripts

```bash
# Setup MCP server
npm run mcp:setup

# Generate database documentation
npm run mcp:docs

# Test MCP server connection
npm run mcp:test

# Run database analysis queries
npm run db:analyze
```

## Benefits for Your Logistics Backend

### 1. **Faster Development**
- Instant database schema understanding
- Quick data analysis without writing SQL
- Automated documentation generation

### 2. **Better Debugging**
- Real-time query execution
- Performance analysis
- Data integrity checks

### 3. **Enhanced Security**
- Read-only access by default
- Granular permission controls
- Audit logging

### 4. **Improved Collaboration**
- Natural language database queries
- Automatic documentation
- Shared understanding of data structure

## Next Steps

1. **Configure Authentication**: Set up proper MySQL user with minimal required permissions
2. **Enable Monitoring**: Configure logging and metrics collection
3. **Create Documentation**: Use `npm run mcp:docs` to generate database documentation
4. **Test Queries**: Start with simple schema exploration queries
5. **Gradual Adoption**: Begin with read-only analysis, consider write operations later if needed

## Support

For issues with the MCP server:
- GitHub: https://github.com/benborla/mcp-server-mysql
- Documentation: Check the official README for detailed configuration options

For logistics backend specific questions:
- Review the generated database documentation
- Use the provided query templates
- Check the analysis scripts in `scripts/database-analysis.sql`
