{"tables": [{"database_name": "channelconnector", "table_name": "orders", "table_type": "BASE TABLE", "engine": "InnoDB", "row_count": 1500, "size_mb": 2.5, "created_at": "2024-01-01 00:00:00", "comment": "Order management table"}], "columns": [{"database_name": "channelconnector", "table_name": "orders", "column_name": "id", "position": 1, "default_value": null, "nullable": "NO", "data_type": "int", "full_type": "int(11)", "key_type": "PRI", "extra": "auto_increment", "comment": "Primary key"}], "foreignKeys": [], "indexes": []}