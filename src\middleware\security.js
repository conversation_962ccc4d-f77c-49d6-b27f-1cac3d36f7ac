/**
 * Security middleware
 */
const helmet = require('helmet');
const hpp = require('hpp');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss-clean');
const logger = require('../utils/logger');

/**
 * Apply security headers using Helmet
 */
exports.securityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * Prevent HTTP Parameter Pollution
 */
exports.preventHPP = hpp({
  whitelist: ['sort', 'fields', 'page', 'limit', 'fby_user_id']
});

/**
 * Sanitize user input from malicious HTML
 */
exports.xssClean = xss();

/**
 * Prevent NoSQL injection attacks
 */
exports.mongoSanitize = mongoSanitize();

/**
 * Request logging middleware
 */
exports.requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log request
  logger.info('Incoming request', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    contentType: req.get('Content-Type'),
    contentLength: req.get('Content-Length')
  });
  
  // Log response when finished
  res.on('finish', () => {
    const duration = Date.now() - start;
    
    logger.info('Request completed', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length')
    });
  });
  
  next();
};

/**
 * Error handling middleware
 */
exports.errorHandler = (err, req, res, next) => {
  logger.error('Unhandled error', {
    error: err.message,
    stack: err.stack,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  // Don't leak error details in production
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(err.status || 500).json({
    success: false,
    message: err.message || 'Internal server error',
    ...(isDevelopment && { stack: err.stack })
  });
};

/**
 * 404 handler
 */
exports.notFoundHandler = (req, res) => {
  logger.warn('Route not found', {
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.get('User-Agent')
  });
  
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
};

/**
 * Security middleware to check for suspicious activity
 */
exports.suspiciousActivityDetector = (req, res, next) => {
  const suspiciousPatterns = [
    /(\.\.|\/etc\/|\/proc\/|\/sys\/)/i, // Path traversal
    /(union|select|insert|update|delete|drop|create|alter)/i, // SQL injection
    /(<script|javascript:|vbscript:|onload|onerror)/i, // XSS
    /(eval\(|setTimeout\(|setInterval\()/i, // Code injection
    /(\${|<%|%>|{{|}})/i // Template injection
  ];
  
  const checkString = (str) => {
    return suspiciousPatterns.some(pattern => pattern.test(str));
  };
  
  const checkObject = (obj) => {
    if (typeof obj === 'string') {
      return checkString(obj);
    }
    
    if (typeof obj === 'object' && obj !== null) {
      return Object.values(obj).some(value => checkObject(value));
    }
    
    return false;
  };
  
  // Check URL
  if (checkString(req.originalUrl)) {
    logger.warn('Suspicious URL detected', {
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    });
    
    return res.status(400).json({
      success: false,
      message: 'Invalid request'
    });
  }
  
  // Check request body
  if (req.body && checkObject(req.body)) {
    logger.warn('Suspicious request body detected', {
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      body: req.body
    });
    
    return res.status(400).json({
      success: false,
      message: 'Invalid request data'
    });
  }
  
  // Check query parameters
  if (req.query && checkObject(req.query)) {
    logger.warn('Suspicious query parameters detected', {
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      query: req.query
    });
    
    return res.status(400).json({
      success: false,
      message: 'Invalid query parameters'
    });
  }
  
  next();
};

/**
 * Middleware to set security-related response headers
 */
exports.securityResponseHeaders = (req, res, next) => {
  // Remove server information
  res.removeHeader('X-Powered-By');
  
  // Set custom security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
  
  next();
};
