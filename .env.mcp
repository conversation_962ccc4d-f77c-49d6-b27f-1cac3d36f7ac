# MySQL MCP Server Configuration for Logistics Backend
# Copy this to your main .env file or source it separately

# Basic MySQL connection settings
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASS=your_mysql_password
MYSQL_DB=
# Leave MY<PERSON><PERSON>_DB empty for multi-database mode to access all databases

# SSL Configuration
MYSQL_SSL=false
MYSQL_SSL_REJECT_UNAUTHORIZED=true

# Performance settings
MYSQL_POOL_SIZE=10
MYSQL_QUERY_TIMEOUT=30000
MYSQL_CACHE_TTL=60000

# Security settings
MYSQL_RATE_LIMIT=100
MYSQL_MAX_QUERY_COMPLEXITY=1000

# Write operation permissions (DISABLED for safety)
ALLOW_INSERT_OPERATION=false
ALLOW_UPDATE_OPERATION=false
ALLOW_DELETE_OPERATION=false
ALLOW_DDL_OPERATION=false

# Schema-specific permissions for your logistics databases
# Format: "schema1:true,schema2:false"
SCHEMA_INSERT_PERMISSIONS=channelconnector:false,hcl:false,cclogs:false
SCHEMA_UPDATE_PERMISSIONS=channelconnector:false,hcl:false,cclogs:false
SCHEMA_DELETE_PERMISSIONS=channelconnector:false,hcl:false,cclogs:false
SCHEMA_DDL_PERMISSIONS=channelconnector:false,hcl:false,cclogs:false

# Multi-DB mode settings
MULTI_DB_WRITE_MODE=false

# Monitoring settings
ENABLE_LOGGING=true
MYSQL_LOG_LEVEL=info
MYSQL_METRICS_ENABLED=true
