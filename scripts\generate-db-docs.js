#!/usr/bin/env node

/**
 * Database Documentation Generator
 * Uses the MCP MySQL server to generate comprehensive database documentation
 */

const fs = require('fs');
const path = require('path');

// Database analysis queries
const queries = {
    databases: "SHOW DATABASES",
    
    tables: `
        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            TABLE_TYPE as 'table_type',
            ENGINE as 'engine',
            TABLE_ROWS as 'row_count',
            ROUND((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024, 2) as 'size_mb',
            CREATE_TIME as 'created_at',
            UPDATE_TIME as 'updated_at',
            TABLE_COMMENT as 'comment'
        FROM information_schema.TABLES 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        ORDER BY TABLE_SCHEMA, TABLE_NAME
    `,
    
    columns: `
        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            COLUMN_NAME as 'column_name',
            ORDINAL_POSITION as 'position',
            COLUMN_DEFAULT as 'default_value',
            IS_NULLABLE as 'nullable',
            DATA_TYPE as 'data_type',
            CHARACTER_MAXIMUM_LENGTH as 'max_length',
            NUMERIC_PRECISION as 'precision',
            NUMERIC_SCALE as 'scale',
            COLUMN_TYPE as 'full_type',
            COLUMN_KEY as 'key_type',
            EXTRA as 'extra',
            COLUMN_COMMENT as 'comment'
        FROM information_schema.COLUMNS 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        ORDER BY TABLE_SCHEMA, TABLE_NAME, ORDINAL_POSITION
    `,
    
    foreignKeys: `
        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            COLUMN_NAME as 'column_name',
            REFERENCED_TABLE_SCHEMA as 'referenced_database',
            REFERENCED_TABLE_NAME as 'referenced_table',
            REFERENCED_COLUMN_NAME as 'referenced_column',
            CONSTRAINT_NAME as 'constraint_name'
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        AND REFERENCED_TABLE_NAME IS NOT NULL
        ORDER BY TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME
    `,
    
    indexes: `
        SELECT 
            TABLE_SCHEMA as 'database_name',
            TABLE_NAME as 'table_name',
            INDEX_NAME as 'index_name',
            COLUMN_NAME as 'column_name',
            NON_UNIQUE as 'non_unique',
            INDEX_TYPE as 'index_type',
            SEQ_IN_INDEX as 'sequence'
        FROM information_schema.STATISTICS 
        WHERE TABLE_SCHEMA NOT IN ('information_schema', 'mysql', 'performance_schema', 'sys')
        ORDER BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX
    `
};

// Generate markdown documentation
function generateMarkdownDocs(data) {
    let markdown = `# Database Documentation\n\n`;
    markdown += `Generated on: ${new Date().toISOString()}\n\n`;
    
    // Database overview
    markdown += `## Database Overview\n\n`;
    const databases = [...new Set(data.tables.map(t => t.database_name))];
    
    databases.forEach(db => {
        const dbTables = data.tables.filter(t => t.database_name === db);
        const totalSize = dbTables.reduce((sum, t) => sum + (parseFloat(t.size_mb) || 0), 0);
        const totalRows = dbTables.reduce((sum, t) => sum + (parseInt(t.row_count) || 0), 0);
        
        markdown += `### ${db}\n`;
        markdown += `- **Tables**: ${dbTables.length}\n`;
        markdown += `- **Total Size**: ${totalSize.toFixed(2)} MB\n`;
        markdown += `- **Total Rows**: ${totalRows.toLocaleString()}\n\n`;
    });
    
    // Tables documentation
    databases.forEach(db => {
        markdown += `## Database: ${db}\n\n`;
        const dbTables = data.tables.filter(t => t.database_name === db);
        
        dbTables.forEach(table => {
            markdown += `### Table: ${table.table_name}\n\n`;
            
            if (table.comment) {
                markdown += `**Description**: ${table.comment}\n\n`;
            }
            
            markdown += `**Details**:\n`;
            markdown += `- **Type**: ${table.table_type}\n`;
            markdown += `- **Engine**: ${table.engine}\n`;
            markdown += `- **Rows**: ${parseInt(table.row_count || 0).toLocaleString()}\n`;
            markdown += `- **Size**: ${parseFloat(table.size_mb || 0).toFixed(2)} MB\n`;
            if (table.created_at) {
                markdown += `- **Created**: ${table.created_at}\n`;
            }
            markdown += `\n`;
            
            // Columns
            const tableColumns = data.columns.filter(c => 
                c.database_name === db && c.table_name === table.table_name
            );
            
            if (tableColumns.length > 0) {
                markdown += `**Columns**:\n\n`;
                markdown += `| Column | Type | Nullable | Default | Key | Extra | Comment |\n`;
                markdown += `|--------|------|----------|---------|-----|-------|----------|\n`;
                
                tableColumns.forEach(col => {
                    markdown += `| ${col.column_name} | ${col.full_type} | ${col.nullable} | ${col.default_value || ''} | ${col.key_type || ''} | ${col.extra || ''} | ${col.comment || ''} |\n`;
                });
                markdown += `\n`;
            }
            
            // Foreign Keys
            const tableFKs = data.foreignKeys.filter(fk => 
                fk.database_name === db && fk.table_name === table.table_name
            );
            
            if (tableFKs.length > 0) {
                markdown += `**Foreign Keys**:\n\n`;
                markdown += `| Column | References | Constraint |\n`;
                markdown += `|--------|------------|------------|\n`;
                
                tableFKs.forEach(fk => {
                    markdown += `| ${fk.column_name} | ${fk.referenced_database}.${fk.referenced_table}.${fk.referenced_column} | ${fk.constraint_name} |\n`;
                });
                markdown += `\n`;
            }
            
            // Indexes
            const tableIndexes = data.indexes.filter(idx => 
                idx.database_name === db && idx.table_name === table.table_name
            );
            
            if (tableIndexes.length > 0) {
                const indexGroups = {};
                tableIndexes.forEach(idx => {
                    if (!indexGroups[idx.index_name]) {
                        indexGroups[idx.index_name] = [];
                    }
                    indexGroups[idx.index_name].push(idx);
                });
                
                markdown += `**Indexes**:\n\n`;
                markdown += `| Index | Columns | Type | Unique |\n`;
                markdown += `|-------|---------|------|--------|\n`;
                
                Object.entries(indexGroups).forEach(([indexName, columns]) => {
                    const columnNames = columns.map(c => c.column_name).join(', ');
                    const isUnique = columns[0].non_unique === '0' ? 'Yes' : 'No';
                    const indexType = columns[0].index_type;
                    
                    markdown += `| ${indexName} | ${columnNames} | ${indexType} | ${isUnique} |\n`;
                });
                markdown += `\n`;
            }
            
            markdown += `---\n\n`;
        });
    });
    
    return markdown;
}

// Generate JSON documentation
function generateJsonDocs(data) {
    return JSON.stringify(data, null, 2);
}

// Main function
async function generateDocumentation() {
    console.log('📚 Generating database documentation...');
    
    // Note: In a real implementation, you would use the MCP client to execute these queries
    // For now, we'll create a template structure
    
    const sampleData = {
        tables: [
            {
                database_name: 'channelconnector',
                table_name: 'orders',
                table_type: 'BASE TABLE',
                engine: 'InnoDB',
                row_count: 1500,
                size_mb: 2.5,
                created_at: '2024-01-01 00:00:00',
                comment: 'Order management table'
            }
        ],
        columns: [
            {
                database_name: 'channelconnector',
                table_name: 'orders',
                column_name: 'id',
                position: 1,
                default_value: null,
                nullable: 'NO',
                data_type: 'int',
                full_type: 'int(11)',
                key_type: 'PRI',
                extra: 'auto_increment',
                comment: 'Primary key'
            }
        ],
        foreignKeys: [],
        indexes: []
    };
    
    // Generate documentation
    const markdownDocs = generateMarkdownDocs(sampleData);
    const jsonDocs = generateJsonDocs(sampleData);
    
    // Write files
    const docsDir = path.join(process.cwd(), 'docs');
    if (!fs.existsSync(docsDir)) {
        fs.mkdirSync(docsDir, { recursive: true });
    }
    
    fs.writeFileSync(path.join(docsDir, 'database-schema.md'), markdownDocs);
    fs.writeFileSync(path.join(docsDir, 'database-schema.json'), jsonDocs);
    
    console.log('✅ Documentation generated:');
    console.log('- docs/database-schema.md');
    console.log('- docs/database-schema.json');
    
    // Generate query templates
    const queryTemplates = `# Database Query Templates

## Common Analysis Queries

### 1. Table Overview
\`\`\`sql
${queries.tables}
\`\`\`

### 2. Column Details
\`\`\`sql
${queries.columns}
\`\`\`

### 3. Foreign Key Relationships
\`\`\`sql
${queries.foreignKeys}
\`\`\`

### 4. Index Information
\`\`\`sql
${queries.indexes}
\`\`\`

## Logistics-Specific Queries

### Orders Analysis
\`\`\`sql
-- Recent orders
SELECT * FROM channelconnector.orders 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
ORDER BY created_at DESC;

-- Orders by channel
SELECT channel, COUNT(*) as order_count, SUM(total_amount) as total_value
FROM channelconnector.orders 
GROUP BY channel
ORDER BY order_count DESC;
\`\`\`

### Products Analysis
\`\`\`sql
-- Product inventory status
SELECT sku, title, inventory_quantity, channel
FROM channelconnector.products 
WHERE inventory_quantity < 10
ORDER BY inventory_quantity ASC;
\`\`\`

### User Activity
\`\`\`sql
-- Active users
SELECT name, email, last_login, role
FROM channelconnector.users 
WHERE last_login >= DATE_SUB(NOW(), INTERVAL 30 DAY)
ORDER BY last_login DESC;
\`\`\`
`;
    
    fs.writeFileSync(path.join(docsDir, 'query-templates.md'), queryTemplates);
    console.log('- docs/query-templates.md');
}

if (require.main === module) {
    generateDocumentation().catch(console.error);
}

module.exports = { generateDocumentation, generateMarkdownDocs, generateJsonDocs };
