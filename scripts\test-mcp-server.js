#!/usr/bin/env node

/**
 * Test MCP MySQL server functionality
 */

const { spawn } = require('child_process');
const path = require('path');

async function testMCPServer() {
    console.log('🧪 Testing MCP MySQL Server...');
    
    // Set environment variables for the test
    const env = {
        ...process.env,
        MYSQL_HOST: '*************',
        MYSQL_PORT: '3306',
        MYSQL_USER: 'san',
        MYSQL_PASS: 'DB@2024+nimda!@',
        MYSQL_DB: '',
        ALLOW_INSERT_OPERATION: 'false',
        ALLOW_UPDATE_OPERATION: 'false',
        ALLOW_DELETE_OPERATION: 'false',
        ENABLE_LOGGING: 'true',
        MYSQL_LOG_LEVEL: 'info'
    };
    
    console.log('🚀 Starting MCP server...');
    
    // Start the MCP server
    const mcpProcess = spawn('npx', ['-y', '@benborla29/mcp-server-mysql'], {
        env: env,
        stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let serverOutput = '';
    let serverError = '';
    
    mcpProcess.stdout.on('data', (data) => {
        serverOutput += data.toString();
        console.log('📤 Server output:', data.toString().trim());
    });
    
    mcpProcess.stderr.on('data', (data) => {
        serverError += data.toString();
        console.log('⚠️  Server error:', data.toString().trim());
    });
    
    // Wait a moment for the server to start
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Send a simple MCP request to list resources
    const listResourcesRequest = {
        jsonrpc: "2.0",
        id: 1,
        method: "resources/list"
    };
    
    console.log('📨 Sending list resources request...');
    mcpProcess.stdin.write(JSON.stringify(listResourcesRequest) + '\n');
    
    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Send a query request
    const queryRequest = {
        jsonrpc: "2.0",
        id: 2,
        method: "tools/call",
        params: {
            name: "mysql_query",
            arguments: {
                sql: "SHOW DATABASES"
            }
        }
    };
    
    console.log('📨 Sending database query request...');
    mcpProcess.stdin.write(JSON.stringify(queryRequest) + '\n');
    
    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Clean up
    mcpProcess.kill();
    
    console.log('\n📊 Test Results:');
    console.log('Server Output:', serverOutput);
    if (serverError) {
        console.log('Server Errors:', serverError);
    }
    
    // Check if the server started successfully
    if (serverOutput.includes('MySQL') || serverOutput.includes('Starting')) {
        console.log('✅ MCP server appears to be working!');
        return true;
    } else {
        console.log('❌ MCP server may have issues');
        return false;
    }
}

if (require.main === module) {
    testMCPServer().then(success => {
        console.log(success ? '\n🎉 MCP server test completed!' : '\n❌ MCP server test failed!');
        process.exit(success ? 0 : 1);
    }).catch(error => {
        console.error('❌ Test error:', error);
        process.exit(1);
    });
}

module.exports = { testMCPServer };
