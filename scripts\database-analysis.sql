-- Database Analysis Queries for Logistics Backend
-- Use these queries with the MCP server to understand your database structure

-- 1. List all databases
SHOW DATABASES;

-- 2. Get table information for all logistics databases
SELECT
    TABLE_SCHEMA as 'Database',
    TABLE_NAME as 'Table',
    TABLE_TYPE as 'Type',
    ENGINE as 'Engine',
    TABLE_ROWS as 'Rows',
    ROUND(DATA_LENGTH / 1024 / 1024, 2) as 'Data Size (MB)',
    ROUND(INDEX_LENGTH / 1024 / 1024, 2) as 'Index Size (MB)',
    CREATE_TIME as 'Created',
    UPDATE_TIME as 'Updated'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
ORDER BY TABLE_SCHEMA, TABLE_ROWS DESC;

-- 3. Get column information for key tables across all databases
SELECT
    TABLE_SCHEMA as 'Database',
    TABLE_NAME as 'Table',
    COLUMN_NAME as 'Column',
    DAT<PERSON>_TYPE as 'Type',
    IS_NULL<PERSON>LE as 'Nullable',
    COLUMN_DEFAULT as 'Default',
    COLUMN_KEY as 'Key',
    EXTRA as 'Extra'
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
AND (TABLE_NAME LIKE '%order%' OR TABLE_NAME LIKE '%product%' OR TABLE_NAME LIKE '%user%' OR TABLE_NAME LIKE '%client%' OR TABLE_NAME LIKE '%channel%')
ORDER BY TABLE_SCHEMA, TABLE_NAME, ORDINAL_POSITION;

-- 4. Get foreign key relationships across all databases
SELECT
    TABLE_SCHEMA as 'Database',
    TABLE_NAME as 'Table',
    COLUMN_NAME as 'Column',
    REFERENCED_TABLE_SCHEMA as 'Referenced Database',
    REFERENCED_TABLE_NAME as 'Referenced Table',
    REFERENCED_COLUMN_NAME as 'Referenced Column',
    CONSTRAINT_NAME as 'Constraint'
FROM information_schema.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_SCHEMA, TABLE_NAME;

-- 5. Get indexes information across all databases
SELECT
    TABLE_SCHEMA as 'Database',
    TABLE_NAME as 'Table',
    INDEX_NAME as 'Index',
    COLUMN_NAME as 'Column',
    NON_UNIQUE as 'Non Unique',
    INDEX_TYPE as 'Type'
FROM information_schema.STATISTICS
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
ORDER BY TABLE_SCHEMA, TABLE_NAME, INDEX_NAME;

-- 6. Check for authentication and user tables across all databases
SELECT TABLE_SCHEMA as 'Database', TABLE_NAME, TABLE_ROWS, CREATE_TIME
FROM information_schema.TABLES
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
AND (TABLE_NAME LIKE '%user%' OR TABLE_NAME LIKE '%auth%' OR TABLE_NAME LIKE '%role%' OR TABLE_NAME LIKE '%permission%')
ORDER BY TABLE_SCHEMA, TABLE_NAME;

-- 7. Check for order and product related tables across all databases
SELECT TABLE_SCHEMA as 'Database', TABLE_NAME, TABLE_ROWS, CREATE_TIME
FROM information_schema.TABLES
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
AND (TABLE_NAME LIKE '%order%' OR TABLE_NAME LIKE '%product%' OR TABLE_NAME LIKE '%inventory%')
ORDER BY TABLE_SCHEMA, TABLE_NAME;

-- 8. Check for channel and integration tables across all databases
SELECT TABLE_SCHEMA as 'Database', TABLE_NAME, TABLE_ROWS, CREATE_TIME
FROM information_schema.TABLES
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
AND (TABLE_NAME LIKE '%channel%' OR TABLE_NAME LIKE '%shopify%' OR TABLE_NAME LIKE '%amazon%' OR TABLE_NAME LIKE '%ebay%')
ORDER BY TABLE_SCHEMA, TABLE_NAME;

-- 9. Get database size information for all logistics databases
SELECT
    table_schema as 'Database',
    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'Size (MB)',
    COUNT(*) as 'Tables'
FROM information_schema.tables
WHERE table_schema IN ('channelconnector', 'hcl', 'cclogs')
GROUP BY table_schema;

-- 10. Check for recent activity columns across all databases
SELECT
    TABLE_SCHEMA as 'Database',
    TABLE_NAME,
    COLUMN_NAME,
    DATA_TYPE
FROM information_schema.COLUMNS
WHERE TABLE_SCHEMA IN ('channelconnector', 'hcl', 'cclogs')
AND (COLUMN_NAME LIKE '%created%' OR COLUMN_NAME LIKE '%updated%' OR COLUMN_NAME LIKE '%timestamp%' OR COLUMN_NAME LIKE '%date%')
ORDER BY TABLE_SCHEMA, TABLE_NAME, COLUMN_NAME;
