#!/usr/bin/env node

/**
 * Generate Claude Desktop configuration from environment variables
 * This ensures no hardcoded values and supports all 3 databases
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

function generateClaudeConfig() {
    console.log('🔧 Generating Claude Desktop configuration from .env file...');
    
    // Validate required environment variables
    const requiredVars = ['DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD'];
    const missingVars = requiredVars.filter(varName => !process.env[varName]);
    
    if (missingVars.length > 0) {
        console.error('❌ Missing required environment variables:', missingVars.join(', '));
        console.error('Please check your .env file');
        return false;
    }
    
    // Get Node.js path for configuration (cross-platform)
    let nodePath = '';
    try {
        if (process.platform === 'win32') {
            nodePath = process.env.PATH || '';
        } else {
            nodePath = '/usr/local/bin:/usr/bin:/bin';
        }
    } catch (error) {
        console.log('⚠️  Could not determine system PATH, using default');
        nodePath = process.env.PATH || '';
    }
    
    // Generate configuration object
    const config = {
        mcpServers: {
            logistics_mysql: {
                command: "npx",
                args: ["-y", "@benborla29/mcp-server-mysql"],
                env: {
                    // Database connection (from existing .env variables)
                    MYSQL_HOST: process.env.DB_HOST,
                    MYSQL_PORT: process.env.DB_PORT,
                    MYSQL_USER: process.env.DB_USER,
                    MYSQL_PASS: process.env.DB_PASSWORD,
                    MYSQL_DB: process.env.MCP_MYSQL_DB || "",
                    
                    // SSL Configuration
                    MYSQL_SSL: process.env.MCP_MYSQL_SSL || "false",
                    MYSQL_SSL_REJECT_UNAUTHORIZED: process.env.MCP_MYSQL_SSL_REJECT_UNAUTHORIZED || "true",
                    
                    // Performance settings
                    MYSQL_POOL_SIZE: process.env.MCP_MYSQL_POOL_SIZE || "10",
                    MYSQL_QUERY_TIMEOUT: process.env.MCP_MYSQL_QUERY_TIMEOUT || "30000",
                    MYSQL_CACHE_TTL: process.env.MCP_MYSQL_CACHE_TTL || "60000",
                    
                    // Rate limiting
                    MYSQL_RATE_LIMIT: process.env.MCP_MYSQL_RATE_LIMIT || "100",
                    MYSQL_MAX_QUERY_COMPLEXITY: process.env.MCP_MYSQL_MAX_QUERY_COMPLEXITY || "1000",
                    
                    // Security settings (read-only by default)
                    ALLOW_INSERT_OPERATION: process.env.MCP_ALLOW_INSERT_OPERATION || "false",
                    ALLOW_UPDATE_OPERATION: process.env.MCP_ALLOW_UPDATE_OPERATION || "false",
                    ALLOW_DELETE_OPERATION: process.env.MCP_ALLOW_DELETE_OPERATION || "false",
                    ALLOW_DDL_OPERATION: process.env.MCP_ALLOW_DDL_OPERATION || "false",
                    
                    // Schema-specific permissions for all 3 databases
                    SCHEMA_INSERT_PERMISSIONS: process.env.MCP_SCHEMA_INSERT_PERMISSIONS || "channelconnector:false,hcl:false,cclogs:false",
                    SCHEMA_UPDATE_PERMISSIONS: process.env.MCP_SCHEMA_UPDATE_PERMISSIONS || "channelconnector:false,hcl:false,cclogs:false",
                    SCHEMA_DELETE_PERMISSIONS: process.env.MCP_SCHEMA_DELETE_PERMISSIONS || "channelconnector:false,hcl:false,cclogs:false",
                    SCHEMA_DDL_PERMISSIONS: process.env.MCP_SCHEMA_DDL_PERMISSIONS || "channelconnector:false,hcl:false,cclogs:false",
                    
                    // Multi-database mode
                    MULTI_DB_WRITE_MODE: "false",
                    
                    // Monitoring
                    ENABLE_LOGGING: process.env.MCP_ENABLE_LOGGING || "true",
                    MYSQL_LOG_LEVEL: process.env.MCP_MYSQL_LOG_LEVEL || "info",
                    MYSQL_METRICS_ENABLED: process.env.MCP_MYSQL_METRICS_ENABLED || "true",
                    
                    // System PATH
                    PATH: nodePath
                }
            }
        }
    };
    
    // Write configuration file
    const configPath = path.join(process.cwd(), 'claude-desktop-config.json');
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    
    console.log(`✅ Claude Desktop configuration generated: ${configPath}`);
    
    // Display configuration summary
    console.log('\n📋 Configuration Summary:');
    console.log(`  Database Host: ${process.env.DB_HOST}`);
    console.log(`  Database Port: ${process.env.DB_PORT}`);
    console.log(`  Database User: ${process.env.DB_USER}`);
    console.log(`  Databases: channelconnector, hcl, cclogs (multi-DB mode)`);
    console.log(`  Security: Read-only mode enabled`);
    console.log(`  Logging: ${process.env.MCP_ENABLE_LOGGING || 'true'}`);
    
    // Display installation instructions
    console.log('\n📝 Installation Instructions:');
    console.log('1. Copy the generated configuration to your Claude Desktop config file:');
    
    if (process.platform === 'win32') {
        console.log('   Windows: %APPDATA%\\Claude\\claude_desktop_config.json');
    } else if (process.platform === 'darwin') {
        console.log('   macOS: ~/Library/Application Support/Claude/claude_desktop_config.json');
    } else {
        console.log('   Linux: ~/.config/claude/claude_desktop_config.json');
    }
    
    console.log('2. Restart Claude Desktop');
    console.log('3. Test by asking: "Show me all databases and their tables"');
    
    // Display example queries for all 3 databases
    console.log('\n💡 Example queries for your logistics databases:');
    console.log('  📊 "Show me all tables in channelconnector database"');
    console.log('  📊 "Analyze the authentication system in hcl database"');
    console.log('  📊 "Show recent logs from cclogs database"');
    console.log('  📊 "What are the foreign key relationships across all databases?"');
    console.log('  📊 "Show me the largest tables by size in each database"');
    
    return true;
}

if (require.main === module) {
    const success = generateClaudeConfig();
    process.exit(success ? 0 : 1);
}

module.exports = { generateClaudeConfig };
