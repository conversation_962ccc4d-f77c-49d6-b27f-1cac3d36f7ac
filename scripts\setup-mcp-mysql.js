#!/usr/bin/env node

/**
 * Setup script for MySQL MCP Server
 * This script helps configure the MCP server for your logistics backend
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Setting up MySQL MCP Server for Logistics Backend...\n');

// Check if Node.js version is compatible
const nodeVersion = process.version;
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

if (majorVersion < 18) {
    console.error('❌ Node.js version 18 or higher is required');
    console.error(`Current version: ${nodeVersion}`);
    process.exit(1);
}

console.log(`✅ Node.js version: ${nodeVersion}`);

// Check if MySQL is accessible
try {
    console.log('🔍 Checking MySQL connection...');
    
    // Try to connect to MySQL (you may need to adjust credentials)
    const mysql = require('mysql2/promise');
    
    async function testConnection() {
        try {
            const connection = await mysql.createConnection({
                host: process.env.MYSQL_HOST || 'localhost',
                port: process.env.MYSQL_PORT || 3306,
                user: process.env.MYSQL_USER || 'root',
                password: process.env.MYSQL_PASS || '',
            });
            
            await connection.execute('SELECT 1');
            await connection.end();
            
            console.log('✅ MySQL connection successful');
            return true;
        } catch (error) {
            console.log('⚠️  MySQL connection failed:', error.message);
            console.log('Please ensure MySQL is running and credentials are correct');
            return false;
        }
    }
    
    testConnection();
    
} catch (error) {
    console.log('⚠️  mysql2 package not found. Installing...');
    try {
        execSync('npm install mysql2', { stdio: 'inherit' });
        console.log('✅ mysql2 installed successfully');
    } catch (installError) {
        console.log('❌ Failed to install mysql2:', installError.message);
    }
}

// Install MCP MySQL server
try {
    console.log('\n📦 Installing MySQL MCP Server...');
    execSync('npm install -g @benborla29/mcp-server-mysql', { stdio: 'inherit' });
    console.log('✅ MySQL MCP Server installed successfully');
} catch (error) {
    console.log('❌ Failed to install MCP server:', error.message);
    console.log('You may need to run with sudo or check your npm permissions');
}

// Get Node.js paths for configuration (Windows compatible)
let nodePath, nodeDir, nodeModulesPath;

try {
    if (process.platform === 'win32') {
        nodePath = execSync('where node', { encoding: 'utf8' }).trim().split('\n')[0];
    } else {
        nodePath = execSync('which node', { encoding: 'utf8' }).trim();
    }

    nodeDir = path.dirname(nodePath);

    if (process.platform === 'win32') {
        nodeModulesPath = path.join(nodeDir, 'node_modules');
    } else {
        nodeModulesPath = path.join(nodeDir, '..', 'lib', 'node_modules');
    }
} catch (error) {
    console.log('⚠️  Could not determine Node.js paths automatically');
    nodePath = 'node';
    nodeDir = '';
    nodeModulesPath = '';
}

console.log('\n📋 Configuration Information:');
console.log(`Node.js binary: ${nodePath}`);
console.log(`Node.js directory: ${nodeDir}`);
console.log(`Node modules path: ${nodeModulesPath}`);

// Create Claude Desktop configuration
const claudeConfig = {
    mcpServers: {
        logistics_mysql: {
            command: "npx",
            args: ["-y", "@benborla29/mcp-server-mysql"],
            env: {
                MYSQL_HOST: "localhost",
                MYSQL_PORT: "3306",
                MYSQL_USER: "root",
                MYSQL_PASS: "your_password",
                MYSQL_DB: "", // Multi-DB mode
                
                // Security settings (read-only by default)
                ALLOW_INSERT_OPERATION: "false",
                ALLOW_UPDATE_OPERATION: "false",
                ALLOW_DELETE_OPERATION: "false",
                ALLOW_DDL_OPERATION: "false",
                
                // Performance settings
                MYSQL_POOL_SIZE: "10",
                MYSQL_QUERY_TIMEOUT: "30000",
                
                // Monitoring
                ENABLE_LOGGING: "true",
                MYSQL_LOG_LEVEL: "info",
                
                // Path configuration (cross-platform)
                PATH: process.platform === 'win32'
                    ? `${nodeDir};${process.env.PATH}`
                    : `${nodeDir}:/usr/local/bin:/usr/bin:/bin`,
                NODE_PATH: nodeModulesPath
            }
        }
    }
};

// Write configuration file
const configPath = path.join(process.cwd(), 'claude-desktop-config.json');
fs.writeFileSync(configPath, JSON.stringify(claudeConfig, null, 2));

console.log(`\n✅ Claude Desktop configuration written to: ${configPath}`);

// Create environment file
const envContent = `# MySQL MCP Server Configuration
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASS=your_password
MYSQL_DB=

# Security (read-only by default)
ALLOW_INSERT_OPERATION=false
ALLOW_UPDATE_OPERATION=false
ALLOW_DELETE_OPERATION=false
ALLOW_DDL_OPERATION=false

# Performance
MYSQL_POOL_SIZE=10
MYSQL_QUERY_TIMEOUT=30000

# Monitoring
ENABLE_LOGGING=true
MYSQL_LOG_LEVEL=info
`;

const envPath = path.join(process.cwd(), '.env.mcp');
fs.writeFileSync(envPath, envContent);

console.log(`✅ Environment configuration written to: ${envPath}`);

console.log('\n🎉 Setup complete!');
console.log('\n📝 Next steps:');
console.log('1. Update the MySQL credentials in .env.mcp');
console.log('2. Copy the configuration to your Claude Desktop config file');
console.log('3. Restart Claude Desktop');
console.log('4. Test the connection by asking Claude to describe your database schema');

console.log('\n🔧 Configuration files created:');
console.log(`- ${configPath}`);
console.log(`- ${envPath}`);
console.log('- scripts/database-analysis.sql');

console.log('\n💡 Usage examples:');
console.log('- "Show me all tables in the channelconnector database"');
console.log('- "Describe the structure of the orders table"');
console.log('- "What are the foreign key relationships in my database?"');
console.log('- "Show me the authentication-related tables"');

console.log('\n⚠️  Security Note:');
console.log('The MCP server is configured in READ-ONLY mode by default.');
console.log('To enable write operations, update the ALLOW_*_OPERATION flags in the configuration.');
